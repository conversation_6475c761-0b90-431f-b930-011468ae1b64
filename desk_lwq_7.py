# 导入必要的库
from machine import TOUCH
import time
import image
import os, sys, gc, struct
from media.sensor import *
from media.display import *
from media.media import *
from machine import UART, FPIOA, TOUCH, Pin, Timer

# 添加内存管理配置
gc.enable()  # 启用自动垃圾回收
gc.threshold(4096)  # 设置垃圾回收阈值


# 串口定义
def receive_data():
    try:
        if uart.any():
            return uart.read().decode().strip()
    except:
        return ""

def send_data(data):
    """发送坐标数据"""
    try:
        # 将数据打包为字节流
        data_byte = struct.pack('<%dh' % len(data), *data)
        serial_data = bytearray()
        serial_data.append(0xFF)  # 头部
        serial_data.extend(data_byte)
        serial_data.append(0xFE)  # 尾部
        uart.write(serial_data)
    except Exception as e:
        print("串口发送错误:", e)

# 初始化UART通信
fpioa = FPIOA()
fpioa.set_function(11, FPIOA.UART2_TXD)
fpioa.set_function(12, FPIOA.UART2_RXD)
uart = UART(UART.UART2, 115200)

# 全局变量
DISPLAY_WIDTH = 800
DISPLAY_HEIGHT = 480
find_roi = [30, 20, 740, 430]  # 主要检测区域
GRID_COLS = 10
GRID_ROWS = 10
#Init_TH = [71, 9, 62, 14, 127, -24]  # 默认阈值
Init_TH_red = [71, 9, 62, 14, 127, -24] # 默认阈值
Init_TH_blue = [66, 34, 56, 22, 45, -12]  # 蓝色阈值 (修复未定义变量)
Init_TH_black = [66, 34, 56, 22, 45, -12]  # 默认阈值
Init_TH_no1 = [66, 6, 6, 6, 6, -6]  # 默认阈值
Init_TH_no2 = [11, 11, 11, 11, 11, -11]  # 默认阈值
#Red_TH = (71, 9, 62, 14, 127, -24)  # 红色阈值 (Lmin, Lmax, Amin, Amax, Bmin, Bmax)

# 初始化阈值文件
def init_threshold_files():
    """初始化阈值文件"""
    thresholds = {}
    for i in range(1, 5):
        file = f'/data/threshold_{i}.txt'
        try:
            # 如果文件不存在则创建
            if file not in os.listdir('/data'):
                with open(file, 'w') as f:
                    f.write(str(Init_TH_red))

            # 读取阈值
            with open(file, 'r') as f:
                thresholds[i] = eval(f.read().strip())
        except:
            thresholds[i] = Init_TH_red.copy()
    return thresholds

# 按键尺寸和位置定义
BTN_WIDTH = 100
BTN_HEIGHT = 40
BTN_MARGIN = 30
BTN_Y_CHANGE = DISPLAY_HEIGHT - 3 * (BTN_HEIGHT + BTN_MARGIN)
BTN_Y_TOP = DISPLAY_HEIGHT - 2 * (BTN_HEIGHT + BTN_MARGIN)
BTN_Y_BOTTOM = DISPLAY_HEIGHT - BTN_HEIGHT - BTN_MARGIN
BTN_X_SIDE = DISPLAY_WIDTH - BTN_MARGIN - BTN_WIDTH

# 菜单定义 - 完全按照第一份代码结构
menu_main = [
    {"label": "Task", "rect": (BTN_X_SIDE, 120, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "Threa", "rect": (BTN_X_SIDE, 120+BTN_HEIGHT+BTN_MARGIN, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "test", "rect": (BTN_X_SIDE, 120+2*BTN_HEIGHT+2*BTN_MARGIN, BTN_WIDTH, BTN_HEIGHT)}
]

menu_return = [
    {"label": "Return", "rect": (BTN_X_SIDE, 120+3*BTN_HEIGHT+3*BTN_MARGIN, BTN_WIDTH, BTN_HEIGHT)}
]

menu_task = [
    {"label": "Task_1", "rect": (BTN_MARGIN, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "Task_2", "rect": (BTN_MARGIN*2+BTN_WIDTH, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "Task_3", "rect": (BTN_MARGIN*3+BTN_WIDTH*2, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "Task_4", "rect": (BTN_MARGIN*4+BTN_WIDTH*3, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)}
]

menu_threshold_select = [
    {"label": "RED_th", "rect": (BTN_MARGIN, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "bule_th", "rect": (BTN_MARGIN*2+BTN_WIDTH, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "NO_th", "rect": (BTN_MARGIN*3+BTN_WIDTH*2, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "NO_th", "rect": (BTN_MARGIN*4+BTN_WIDTH*3, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "enter", "rect": (BTN_MARGIN*5+BTN_WIDTH*4, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)}
]

menu_threshold_adjust_top = [
    {"label": "TH_+", "rect": (BTN_MARGIN, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "TH_-", "rect": (BTN_MARGIN*2+BTN_WIDTH, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "img", "rect": (BTN_MARGIN*3+BTN_WIDTH*2, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "save_th", "rect": (BTN_MARGIN*4+BTN_WIDTH*3, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "init_th", "rect": (BTN_MARGIN*5+BTN_WIDTH*4, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)}
]

menu_threshold_adjust_bottom = [
    {"label": "L_min", "rect": (BTN_MARGIN, BTN_Y_BOTTOM, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "L_max", "rect": (BTN_MARGIN+BTN_WIDTH+BTN_MARGIN, BTN_Y_BOTTOM, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "A_min", "rect": (BTN_MARGIN+2*(BTN_WIDTH+BTN_MARGIN), BTN_Y_BOTTOM, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "A_max", "rect": (BTN_MARGIN+3*(BTN_WIDTH+BTN_MARGIN), BTN_Y_BOTTOM, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "B_min", "rect": (BTN_MARGIN+4*(BTN_WIDTH+BTN_MARGIN), BTN_Y_BOTTOM, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "B_max", "rect": (BTN_MARGIN+5*(BTN_WIDTH+BTN_MARGIN), BTN_Y_BOTTOM, BTN_WIDTH, BTN_HEIGHT)}
]

menu_test = [
    {"label": "Test_1", "rect": (BTN_MARGIN, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "Test_2", "rect": (BTN_MARGIN*2+BTN_WIDTH, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "Test_3", "rect": (BTN_MARGIN*3+BTN_WIDTH*2, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)},
    {"label": "Test_4", "rect": (BTN_MARGIN*4+BTN_WIDTH*3, BTN_Y_TOP, BTN_WIDTH, BTN_HEIGHT)}
]

# 状态变量 - 完全按照第一份代码逻辑
class AppState:
    def __init__(self):
        # 菜单标志位
        self.menu_flag = 0  # 0:主菜单, 1:任务, 2:阈值, 3:测试

        # 任务相关
        self.Task_flag_num = 0

        # 测试相关
        self.Test_flag_num = 0

        # 阈值相关
        self.thresholds = init_threshold_files()
        self.threshold_change_select = 1  # 当前选择的阈值ID
        self.enter_flag = 0  # 是否进入阈值调整界面
        self.threshold_index = 0  # 当前调整的阈值参数索引
        self.binary_flag = 0  # 是否显示二值化图像

        # 性能统计
        self.fps = 0
        self.last_frame_time = time.ticks_ms()
        self.frame_count = 0

        # 触摸状态
        self.points_flag = 0
        self.last_touch_time = time.ticks_ms()
        self.touch_cooldown = 150  # 触摸冷却时间(ms)

       #任务3参数
        self.frame_count = 0
        self.points_flag = 0
        self.min_roi_w = 20
        self.min_roi_h = 15
        self.task_flag_img=0
state = AppState()

#----------------------------- 初始化传感器和显示 -------------------------------
def init_sensor_display():
    sensor = Sensor()
    sensor.reset()
    # 配置多通道出图
    sensor.set_framesize(width=800, height=480, chn=CAM_CHN_ID_0)
    sensor.set_pixformat(Sensor.RGB565, chn=CAM_CHN_ID_0)
    Display.init(Display.ST7701, width=DISPLAY_WIDTH, height=DISPLAY_HEIGHT, osd_num=1, to_ide=True)
    MediaManager.init()
    sensor.run()
    return sensor

sensor = init_sensor_display()
# ❌ 移除全局图像对象创建，避免内存冲突
# osd_img = image.Image(DISPLAY_WIDTH, DISPLAY_HEIGHT, image.ARGB8888)
tp = TOUCH(0)

#----------------------------- 阈值文件操作 -------------------------------
def save_threshold(threshold_id):
    """保存阈值到文件"""
    try:
        with open(f'/data/threshold_{threshold_id}.txt', 'w') as f:
            f.write(str(state.thresholds[threshold_id]))
        return True
    except:
        return False

#----------------------------- 绘制函数 -------------------------------
def draw_button(img, x, y, w, h, label, color=(0, 128, 255)):
    """绘制带标签的按钮"""
    img.draw_rectangle(x, y, w, h, color=color, thickness=-1)
    img.draw_rectangle(x, y, w, h, color=(255, 255, 255), thickness=2)
    img.draw_string_advanced(x + 8, y + 12, 20, label, color=(255, 255, 255))

def draw_menu(img, menu):
    """绘制菜单项"""
    for btn in menu:
        draw_button(img, *btn["rect"], btn["label"])

def draw_threshold_ui(img):
    """绘制UI界面 - 按照第一份代码逻辑"""
    # 绘制返回按钮
    draw_menu(img, menu_return)

    # 根据菜单标志位绘制不同界面
    if state.menu_flag == 0:  # 主菜单
        draw_menu(img, menu_main)
    elif state.menu_flag == 1:  # 任务菜单
        draw_menu(img, menu_task)
    elif state.menu_flag == 2:  # 阈值菜单
        if state.enter_flag == 0:  # 阈值选择界面
            draw_menu(img, menu_threshold_select)
        else:  # 阈值调整界面
            draw_menu(img, menu_threshold_adjust_top)
            draw_menu(img, menu_threshold_adjust_bottom)
    elif state.menu_flag == 3:  # 测试菜单
        draw_menu(img, menu_test)

    # 显示状态信息 - 按照第一份代码格式

    if state.menu_flag == 0:  # 主菜单
        img.draw_string_advanced(380, 0, 20, f"菜单界面: {state.menu_flag}", color=(255, 255, 0))
        #img.draw_string_advanced(0, 0, 20, f"Val_TH: {state.thresholds[state.threshold_change_select]}", color=(255, 192, 203))
        img.draw_string_advanced(0, 0, 20, f"红色: {state.thresholds[1]}", color=(255, 192, 203))
        img.draw_string_advanced(0, 30, 20, f"蓝色: {state.thresholds[2]}", color=(255, 192, 203))
    elif state.menu_flag == 1:  # 任务菜单
        img.draw_string_advanced(380, 0, 20, f"任务界面: {state.menu_flag}", color=(255, 255, 0))
        img.draw_string_advanced(DISPLAY_WIDTH-160, 30, 20, f"任务: {state.Task_flag_num}", color=(255, 255, 0))
    elif state.menu_flag == 2:  # 阈值菜单
        img.draw_string_advanced(380, 0, 20, f"th值界面: {state.menu_flag}", color=(255, 255, 0))
        img.draw_string_advanced(380, 30, 20, f"s_flag: {state.enter_flag}", color=(255, 255, 0))
        img.draw_string_advanced(DISPLAY_WIDTH-160, 30, 20, f"bot_key: {state.threshold_index}", color=(255, 255, 0))
        img.draw_string_advanced(0, 0, 20, f"调整th值: {state.thresholds[state.threshold_change_select]}", color=(255, 192, 203))
        img.draw_string_advanced(0, 60, 20, f"选择th界面: {state.threshold_change_select}", color=(255, 255, 0))
        if state.threshold_change_select ==1:
            img.draw_string_advanced(0, 30, 20, f"初始th值1: {Init_TH_red}", color=(255, 128, 255))
        elif state.threshold_change_select ==2:
            img.draw_string_advanced(0, 30, 20, f"初始th值2: {Init_TH_blue}", color=(255, 128, 255))
        elif state.threshold_change_select ==3:
            img.draw_string_advanced(0, 30, 20, f"初始th值3: {Init_TH_no1}", color=(255, 128, 255))
        elif state.threshold_change_select ==4:
            img.draw_string_advanced(0, 30, 20, f"初始th值4: {Init_TH_no2}", color=(255, 128, 255))

    elif state.menu_flag == 3:  # 测试菜单
        img.draw_string_advanced(380, 0, 20, f"测试界面: {state.menu_flag}", color=(255, 255, 0))
        img.draw_string_advanced(DISPLAY_WIDTH-160, 30, 20, f"测试: {state.Test_flag_num}", color=(255, 255, 0))

    # 显示FPS
    img.draw_string_advanced(DISPLAY_WIDTH-160, 0, 20, f"FPS: {state.fps:.1f}", color=(255, 255, 0))

#----------------------------- 触摸处理 -------------------------------
def handle_touch(points):
    """处理触摸事件 - 完全按照第一份代码逻辑"""
    if not points:
        state.points_flag = 0
        return

    current_time = time.ticks_ms()
    if time.ticks_diff(current_time, state.last_touch_time) < state.touch_cooldown:
        return

    state.last_touch_time = current_time
    x, y = points[0].x, points[0].y

    # 返回按钮处理
    return_btn = menu_return[0]
    rx, ry, rw, rh = return_btn["rect"]
    if (rx <= x <= rx+rw) and (ry <= y <= ry+rh):
        # 返回主菜单时重置所有状态
        state.menu_flag = 0
        state.threshold_index = 0
        state.binary_flag = 0
        state.Test_flag_num = 0
        state.Task_flag_num = 0
        state.enter_flag = 0
        #任务1参数
        state.frame_count = 0
        state.points_flag = 0
        state.min_roi_w = 20
        state.min_roi_h = 15
        state.task_flag_img=0
        return

    # 主菜单处理
    if state.menu_flag == 0:
        for i, btn in enumerate(menu_main):
            bx, by, bw, bh = btn["rect"]
            if (bx <= x <= bx+bw) and (by <= y <= by+bh):
                state.menu_flag = i + 1
                break

    # 任务菜单处理
    elif state.menu_flag == 1:
        for i, btn in enumerate(menu_task):
            bx, by, bw, bh = btn["rect"]
            if (bx <= x <= bx+bw) and (by <= y <= by+bh):
                state.Task_flag_num = i + 1
                break

    # 阈值菜单处理
    elif state.menu_flag == 2:
        if state.enter_flag == 0:  # 阈值选择界面
            for i, btn in enumerate(menu_threshold_select):
                bx, by, bw, bh = btn["rect"]
                if (bx <= x <= bx+bw) and (by <= y <= by+bh):
                    if i < 4:  # 选择阈值1-4
                        state.threshold_change_select = i + 1
                    else:  # 确认按钮
                        state.enter_flag = 1
                    break
        elif state.enter_flag == 1:  # 阈值调整界面
            # 顶部按钮处理
            for i, btn in enumerate(menu_threshold_adjust_top):
                bx, by, bw, bh = btn["rect"]
                if (bx <= x <= bx+bw) and (by <= y <= by+bh):
                    current_th = state.thresholds[state.threshold_change_select]

                    if i == 0:  # TH_+
                        current_th[state.threshold_index] = min(current_th[state.threshold_index] + 1,
                                                              100 if state.threshold_index < 2 else 127)

                    elif i == 1:  # TH_-
                        current_th[state.threshold_index] = max(current_th[state.threshold_index] - 1,
                                                              0 if state.threshold_index < 2 else -128)

                    elif i == 2:  # 切换图像
                        state.binary_flag = not state.binary_flag

                    elif i == 3:  # 保存阈值
                        save_threshold(state.threshold_change_select)

                    elif i == 4:  # 初始化阈值
                        if state.threshold_change_select ==1:
                            current_th[:] = Init_TH_red.copy()
                        elif state.threshold_change_select ==2:
                            current_th[:] = Init_TH_blue.copy()
                        elif state.threshold_change_select ==3:
                            current_th[:] = Init_TH_no1.copy()
                        elif state.threshold_change_select ==4:
                            current_th[:] = Init_TH_no2.copy()
                    state.thresholds[state.threshold_change_select] = current_th
                    break

            # 底部按钮处理
            for i, btn in enumerate(menu_threshold_adjust_bottom):
                bx, by, bw, bh = btn["rect"]
                if (bx <= x <= bx+bw) and (by <= y <= by+bh):
                    state.threshold_index = i
                    break

    # 测试菜单处理
    elif state.menu_flag == 3:
        for i, btn in enumerate(menu_test):
            bx, by, bw, bh = btn["rect"]
            if (bx <= x <= bx+bw) and (by <= y <= by+bh):
                state.Test_flag_num = i + 1
                break

#----------------------------- 任务处理函数 -------------------------------
#def task_1_processing(img):
#    # 绘制搜索区域
#    img.draw_rectangle(find_roi, color=(0, 255, 0), thickness=2)

#    # 查找红色色块
#    red_blobs = img.find_blobs([state.thresholds[1]], roi=find_roi, threshold=2000, merge=False, pixels_area=50)

#    if not red_blobs:
#        return

#    # 找到最大的色块
#    max_blob = max(red_blobs, key=lambda b: b.pixels())
#    x, y, w, h = max_blob.rect()
#    img.draw_rectangle(x, y, w, h, color=(255, 0, 0), thickness=2)

#    # 确定分割方向
#    if w >= h:  # 横向分割
#        # 计算分割数量（基于固定宽度）
#        num_segments = max(1, w // state.min_roi_w)

#        # 实际分割宽度（考虑剩余像素）
#        segment_width = min(state.min_roi_w, w)

#        for i in range(num_segments):
#            # 计算当前分割位置
#            seg_x = x + i * segment_width
#            seg_w = segment_width

#            # 处理最后一个分割区域（可能小于固定宽度）
#            if i == num_segments - 1:
#                seg_w = w - i * segment_width

#            # 创建分割区域
#            roi = [seg_x, y, seg_w, h]
#            #img.draw_rectangle(roi, color=(0, 255, 0), thickness=1)  # 可视化分割区域

#            # 在分割区域内查找色块
#            sub_blobs = img.find_blobs([state.thresholds[1]], roi=roi, merge=False, pixels_area=15)

#            if sub_blobs:
#                largest_sub = max(sub_blobs, key=lambda b: b.pixels())
#                img.draw_circle(largest_sub.cx(), largest_sub.cy(), 10, color=(0, 0, 255), thickness=3)
#                centr_cx=largest_sub.cx()
#                centr_cy=largest_sub.cy()
#                #此处发送坐标
#                ######
#                #####
#                #####
#                #####
#                ##
#                #
#                print("第%d个:"%i)
#                print("中心坐标:",centr_cx,centr_cy)
#    else:  # 纵向分割
#        # 计算分割数量（基于固定高度）
#        num_segments = max(1, h // state.min_roi_h)

#        # 实际分割高度（考虑剩余像素）
#        segment_height = min(state.min_roi_h, h)

#        for i in range(num_segments):
#            # 计算当前分割位置
#            seg_y = y + i * segment_height
#            seg_h = segment_height

#            # 处理最后一个分割区域（可能小于固定高度）
#            if i == num_segments - 1:
#                seg_h = h - i * segment_height

#            # 创建分割区域
#            roi = [x, seg_y, w, seg_h]
#            #img.draw_rectangle(roi, color=(0, 255, 0), thickness=1)  # 可视化分割区域

#            # 在分割区域内查找色块
#            sub_blobs = img.find_blobs([state.thresholds[1]], roi=roi, merge=False, pixels_area=15)

#            if sub_blobs:
#                largest_sub = max(sub_blobs, key=lambda b: b.pixels())
#                img.draw_circle(largest_sub.cx(), largest_sub.cy(), 10, color=(0, 0, 255), thickness=3)
#                centr_cx=largest_sub.cx()
#                centr_cy=largest_sub.cy()
#                #此处发送坐标
#                ######
#                #####
#                #####
#                #####
#                ##
#                #
#                print("第%d个:"%i)
#                print("中心坐标:",centr_cx,centr_cy)
point_4=[(0,0),(0,0),(0,0),(0,0)]
def find_black(img):
    """查找黑色区域 - 修复内存泄漏问题"""
    global point_4
    try:
        black_blobs = img.find_blobs([state.thresholds[2]], roi=find_roi, threshold=1000, merge=False, pixels_area=50)
        if not black_blobs:
            return 0
        max_blob = max(black_blobs, key=lambda b: b.pixels())
        x, y, w, h = max_blob.rect()
        img.draw_rectangle((x, y, w, h), thickness = 3,color=(0, 0, 255))

        # 查找矩形
        rects = img.find_rects(roi=(x, y, w, h),threshold=2000)
        if rects:
            max_area = 0
            max_rect = None
            for rect in rects:
                area = rect.w() * rect.h()
                if area > max_area and 40 < rect.w() < 200 and 40 < rect.h() < 200:
                    max_area = area
                    max_rect = rect

            if max_rect:
                corners = max_rect.corners()
                point_4 = corners
                return 1

        # ❌ 移除错误的 del img - 不要删除传入的图像对象
        # del img  # 这会导致内存错误
        return 0

    except Exception as e:
        print(f"find_black 错误: {e}")
        gc.collect()
        return 0


def task_1_processing(img):
    img.draw_rectangle(find_roi, color=(0, 255, 0), thickness=2)
    find_black(img)
    if point_4:
        print(point_4)
#        for i in range(2):
#            img.draw_line(point_4[i][0], point_4[i][1], point_4[(i+1)][0], point_4[(i+1)][1], color=(255, 0, 0),thickness=3)



#----------------------------- 主循环 -------------------------------
#----------------------------- 主循环 -------------------------------
def main_loop():
    try:
        """主循环函数 - 修复内存管理问题"""
        global sensor, state

        clock = time.clock()
        running = True  # 控制主循环的标志

        # 内存管理优化
        frame_count = 0
        gc_interval = 30  # 每30帧强制垃圾回收

        while running:
            frame_count += 1

            # 定期垃圾回收
            if frame_count % gc_interval == 0:
                gc.collect()

            clock.tick()

            # 获取图像 - 每次都是新的图像对象
            try:
                osd_img = sensor.snapshot(chn=CAM_CHN_ID_0)
                if osd_img is None:
                    print("警告: 获取图像失败")
                    continue

            except Exception as e:
                print(f"图像获取错误: {e}")
                gc.collect()
                time.sleep_ms(10)
                continue

            # 处理二值化
            if state.binary_flag:
                osd_img.binary([state.thresholds[state.threshold_change_select]])

            # 处理任务
            if state.menu_flag == 1 and state.Task_flag_num == 1:
                print("任务1开始执行")
                task_1_processing(osd_img)
                print("任务1执行结束")
            elif state.menu_flag == 1 and state.Task_flag_num == 2:
                print("任务2开始执行")
                #task_2_processing(osd_img)
                print("任务2执行结束")
                state.Task_flag_num = 0

            # 处理触摸
            points = tp.read()
            handle_touch(points)

            # 绘制UI
            draw_threshold_ui(osd_img)

            # 计算FPS
            state.fps = clock.fps()

            # 显示图像
            try:
                Display.show_image(osd_img)
            except Exception as e:
                print(f"显示错误: {e}")

            # 内存监控
            if frame_count % 60 == 0:  # 每60帧打印一次内存信息
                print("可用内存:", gc.mem_free())
                print("已用内存:", gc.mem_alloc())

            # 限速以降低CPU使用率
            time.sleep_ms(16)  # 约60FPS

    except KeyboardInterrupt:
        print("用户手动停止 → 强制退出")
        running = False
    except Exception as e:
        print(f"系统异常: {e}")
        running = False
    finally:
        # 资源释放
        print("开始释放资源 → 强制清理")
        try:
            if 'sensor' in locals() and sensor:
                print("停止传感器...")
                sensor.stop()
                time.sleep_ms(200)
                sensor.deinit()
        except Exception as e:
            print(f"传感器释放异常: {e}")

        try:
            print("反初始化显示...")
            Display.deinit()
            time.sleep_ms(200)
        except Exception as e:
            print(f"显示释放异常: {e}")

        try:
            print("反初始化媒体管理器...")
            MediaManager.deinit()
            time.sleep_ms(200)
        except Exception as e:
            print(f"媒体管理器释放异常: {e}")

        try:
            print("关闭UART...")
            if 'uart' in locals() and uart:
                uart.deinit()
        except Exception as e:
            print(f"UART关闭异常: {e}")

        # 强制垃圾回收
        gc.collect()
        print("程序退出 → 资源已释放")

# 启动主循环
if __name__ == "__main__":
    main_loop()
